using System;
using System.Linq;
using System.Threading.Tasks;
using DriverManagementSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace DriverManagementSystem.Helpers
{
    /// <summary>
    /// مولد الأكواد الفريدة للزيارات الميدانية
    /// </summary>
    public static class VisitCodeGenerator
    {
        /// <summary>
        /// توليد كود فريد جديد للزيارة الميدانية
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        /// <returns>كود فريد للزيارة</returns>
        public static async Task<string> GenerateUniqueVisitCodeAsync(ApplicationDbContext context)
        {
            try
            {
                // الحصول على أعلى رقم موجود
                var lastVisit = await context.FieldVisits
                    .Where(fv => fv.UniqueVisitCode.StartsWith("FV"))
                    .OrderByDescending(fv => fv.Id)
                    .FirstOrDefaultAsync();

                int nextNumber = 1;
                
                if (lastVisit != null && !string.IsNullOrEmpty(lastVisit.UniqueVisitCode))
                {
                    // استخراج الرقم من الكود الموجود
                    var codeNumber = lastVisit.UniqueVisitCode.Substring(2);
                    if (int.TryParse(codeNumber, out int currentNumber))
                    {
                        nextNumber = currentNumber + 1;
                    }
                }

                // توليد الكود الجديد
                string newCode = $"FV{nextNumber:D6}"; // FV000001, FV000002, etc.

                // التحقق من عدم وجود الكود مسبقاً (احتياط إضافي)
                while (await context.FieldVisits.AnyAsync(fv => fv.UniqueVisitCode == newCode))
                {
                    nextNumber++;
                    newCode = $"FV{nextNumber:D6}";
                }

                System.Diagnostics.Debug.WriteLine($"✅ Generated unique visit code: {newCode}");
                return newCode;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error generating unique visit code: {ex.Message}");
                
                // في حالة الخطأ، استخدم timestamp كبديل
                var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
                return $"FV{timestamp}";
            }
        }

        /// <summary>
        /// التحقق من صحة كود الزيارة
        /// </summary>
        /// <param name="visitCode">كود الزيارة</param>
        /// <returns>true إذا كان الكود صحيح</returns>
        public static bool IsValidVisitCode(string visitCode)
        {
            if (string.IsNullOrEmpty(visitCode))
                return false;

            // يجب أن يبدأ بـ FV ويتبعه 6 أرقام على الأقل
            return visitCode.StartsWith("FV") && 
                   visitCode.Length >= 8 && 
                   visitCode.Substring(2).All(char.IsDigit);
        }

        /// <summary>
        /// إصلاح الأكواد المفقودة للزيارات الموجودة
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        /// <returns>عدد الزيارات التي تم إصلاحها</returns>
        public static async Task<int> FixMissingVisitCodesAsync(ApplicationDbContext context)
        {
            try
            {
                var visitsWithoutCodes = await context.FieldVisits
                    .Where(fv => string.IsNullOrEmpty(fv.UniqueVisitCode))
                    .OrderBy(fv => fv.Id)
                    .ToListAsync();

                if (!visitsWithoutCodes.Any())
                {
                    System.Diagnostics.Debug.WriteLine("✅ All visits already have unique codes");
                    return 0;
                }

                System.Diagnostics.Debug.WriteLine($"🔧 Fixing {visitsWithoutCodes.Count} visits without codes");

                // الحصول على أعلى رقم موجود
                var lastCodedVisit = await context.FieldVisits
                    .Where(fv => !string.IsNullOrEmpty(fv.UniqueVisitCode) && fv.UniqueVisitCode.StartsWith("FV"))
                    .OrderByDescending(fv => fv.UniqueVisitCode)
                    .FirstOrDefaultAsync();

                int startNumber = 1;
                if (lastCodedVisit != null && !string.IsNullOrEmpty(lastCodedVisit.UniqueVisitCode))
                {
                    var codeNumber = lastCodedVisit.UniqueVisitCode.Substring(2);
                    if (int.TryParse(codeNumber, out int currentNumber))
                    {
                        startNumber = currentNumber + 1;
                    }
                }

                // تعيين أكواد للزيارات المفقودة
                foreach (var visit in visitsWithoutCodes)
                {
                    visit.UniqueVisitCode = $"FV{startNumber:D6}";
                    startNumber++;
                }

                await context.SaveChangesAsync();
                
                System.Diagnostics.Debug.WriteLine($"✅ Fixed {visitsWithoutCodes.Count} visits with new unique codes");
                return visitsWithoutCodes.Count;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error fixing missing visit codes: {ex.Message}");
                return 0;
            }
        }
    }
}
