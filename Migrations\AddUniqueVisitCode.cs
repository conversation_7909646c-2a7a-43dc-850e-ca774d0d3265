using Microsoft.EntityFrameworkCore.Migrations;

namespace DriverManagementSystem.Migrations
{
    /// <summary>
    /// إضافة كود فريد للزيارات الميدانية لحل مشكلة التكرار
    /// </summary>
    public partial class AddUniqueVisitCode : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // إضافة عمود الكود الفريد للزيارة
            migrationBuilder.AddColumn<string>(
                name: "UniqueVisitCode",
                table: "FieldVisits",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            // إنشاء فهرس فريد للكود
            migrationBuilder.CreateIndex(
                name: "IX_FieldVisits_UniqueVisitCode",
                table: "FieldVisits",
                column: "UniqueVisitCode",
                unique: true);

            // تحديث الزيارات الموجودة بأكواد فريدة
            migrationBuilder.Sql(@"
                UPDATE FieldVisits 
                SET UniqueVisitCode = 'FV' + RIGHT('000000' + CAST(Id AS VARCHAR), 6)
                WHERE UniqueVisitCode = '' OR UniqueVisitCode IS NULL
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // حذف الفهرس
            migrationBuilder.DropIndex(
                name: "IX_FieldVisits_UniqueVisitCode",
                table: "FieldVisits");

            // حذف العمود
            migrationBuilder.DropColumn(
                name: "UniqueVisitCode",
                table: "FieldVisits");
        }
    }
}
