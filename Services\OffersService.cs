using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Models;
using DriverManagementSystem.Data;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// تنفيذ خدمة إدارة عروض الأسعار
    /// </summary>
    public class OffersService : IOffersService
    {
        private readonly ApplicationDbContext _context;

        public OffersService(ApplicationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public OffersService()
        {
            _context = new ApplicationDbContext();
        }

        /// <summary>
        /// حفظ عروض الأسعار للزيارة
        /// </summary>
        public async Task<bool> SaveOffersAsync(List<DriverOffer> selectedOffers)
        {
            try
            {
                if (selectedOffers?.Any() != true)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد عروض للحفظ");
                    return false;
                }

                // حفظ تفاصيل العروض في جدول DriverQuotes
                foreach (var offer in selectedOffers)
                {
                    var driverQuote = offer.ToDriverQuote();
                    driverQuote.Notes = $"Visit: {offer.VisitNumber}" + (offer.IsWinner ? "-فائز" : ""); // ربط بالزيارة
                    _context.DriverQuotes.Add(driverQuote);
                }

                await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"✅ تم حفظ {selectedOffers.Count} عرض للزيارة {selectedOffers.First().VisitNumber}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ العروض: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حفظ عروض الأسعار للزيارة بالرقم
        /// </summary>
        public async Task<bool> SaveVisitOffersAsync(List<DriverOffer> selectedOffers, string visitNumber, int daysCount)
        {
            try
            {
                if (selectedOffers?.Any() != true)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد عروض للحفظ");
                    return false;
                }

                if (string.IsNullOrWhiteSpace(visitNumber))
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ رقم الزيارة غير صحيح");
                    return false;
                }

                System.Diagnostics.Debug.WriteLine($"💾 بدء حفظ {selectedOffers.Count} عرض للزيارة: {visitNumber}");

                // توليد رقم فريد مختصر للزيارة
                var uniqueVisitId = $"V{visitNumber}";
                System.Diagnostics.Debug.WriteLine($"🔑 تم توليد رقم فريد للزيارة: {uniqueVisitId}");

                // حذف العروض القديمة أولاً
                await DeleteVisitOffersAsync(visitNumber);

                // حفظ العروض الجديدة مع منع التكرار
                var savedDrivers = new HashSet<string>();
                foreach (var offer in selectedOffers)
                {
                    // تجنب تكرار نفس السائق
                    if (!savedDrivers.Contains(offer.DriverName))
                    {
                        var driverQuote = new DriverQuote
                        {
                            DriverId = offer.DriverId,
                            DriverName = offer.DriverName,
                            DriverCode = offer.DriverCode,
                            PhoneNumber = offer.PhoneNumber,
                            VehicleType = offer.VehicleType,
                            VehicleNumber = uniqueVisitId, // استخدام VehicleNumber كرقم فريد للزيارة
                            QuotedPrice = offer.ProposedAmount,
                            QuotedDays = offer.DaysCount,
                            // حفظ حالة الفوز بشكل صحيح
                            Status = offer.IsWinner ? QuoteStatus.Accepted : QuoteStatus.Pending,
                            // إضافة معلومة الفوز في الملاحظات لضمان الحفظ
                            Notes = $"Visit: {visitNumber}" + (offer.IsWinner ? "-فائز" : ""),
                            QuoteDate = DateTime.Now
                        };

                        _context.DriverQuotes.Add(driverQuote);
                        savedDrivers.Add(offer.DriverName);
                        System.Diagnostics.Debug.WriteLine($"✅ تم إضافة عرض: {offer.DriverName} - {offer.FormattedAmount} - فائز: {offer.IsWinner}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ تم تجاهل تكرار السائق: {offer.DriverName}");
                    }
                }

                await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"✅ تم حفظ {selectedOffers.Count} عرض للزيارة {visitNumber} بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ العروض: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حفظ عروض الأسعار للزيارة (النسخة القديمة)
        /// </summary>
        public async Task<bool> SaveOffersAsync(int visitId, string visitNumber, List<DriverOffer> selectedOffers)
        {
            try
            {
                if (selectedOffers?.Any() != true)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد عروض للحفظ");
                    return false;
                }

                // تحويل العروض إلى نص
                var offersText = string.Join(" | ", selectedOffers.Select(o => o.ToSaveString()));
                var daysCount = selectedOffers.First().DaysCount;

                // حفظ كنص في جدول الزيارات أو جدول منفصل
                var success = await SaveOffersTextAsync(visitNumber, offersText, daysCount);

                if (success)
                {
                    // حفظ تفاصيل العروض في جدول DriverQuotes
                    foreach (var offer in selectedOffers)
                    {
                        var driverQuote = offer.ToDriverQuote();
                        _context.DriverQuotes.Add(driverQuote);
                    }

                    await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم حفظ {selectedOffers.Count} عرض للزيارة {visitNumber}");
                }

                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ العروض: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حفظ عروض الأسعار كنص
        /// </summary>
        public async Task<bool> SaveOffersTextAsync(string visitNumber, string offersText, int daysCount)
        {
            try
            {
                // البحث عن الزيارة الميدانية
                var fieldVisit = await _context.FieldVisits
                    .FirstOrDefaultAsync(fv => fv.VisitNumber == visitNumber);

                if (fieldVisit != null)
                {
                    // إضافة العروض إلى ملاحظات الزيارة أو حقل مخصص
                    // يمكن إضافة حقل جديد للعروض في نموذج FieldVisit
                    
                    // مؤقتاً: إضافة إلى الملاحظات
                    var offersNote = $"\n--- عروض الأسعار ({DateTime.Now:yyyy/MM/dd}) ---\n{offersText}";
                    
                    // يمكن إضافة حقل OffersText إلى FieldVisit لاحقاً
                    // fieldVisit.OffersText = offersText;
                    
                    await _context.SaveChangesAsync();
                    return true;
                }
                else
                {
                    // إنشاء سجل جديد في جدول منفصل للعروض
                    return await CreateVisitOffersRecord(visitNumber, offersText, daysCount);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ نص العروض: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على عروض الأسعار للزيارة
        /// </summary>
        public async Task<List<DriverOffer>> GetVisitOffersAsync(int visitId)
        {
            try
            {
                var offers = new List<DriverOffer>();

                // البحث في جدول DriverQuotes
                var driverQuotes = await _context.DriverQuotes
                    .Where(dq => dq.Status == QuoteStatus.Accepted)
                    .ToListAsync();

                // جلب جميع السائقين للمطابقة مع العروض
                var allDrivers = await _context.Drivers.ToListAsync();

                foreach (var quote in driverQuotes)
                {
                    // البحث عن السائق في الجدول الرئيسي لجلب رقم التلفون الصحيح
                    var driver = allDrivers.FirstOrDefault(d =>
                        d.Name == quote.DriverName ||
                        d.DriverCode == quote.DriverCode ||
                        d.Id == quote.DriverId);

                    var offer = new DriverOffer
                    {
                        DriverId = quote.DriverId,
                        DriverName = quote.DriverName,
                        DriverCode = quote.DriverCode,
                        PhoneNumber = driver?.PhoneNumber ?? quote.PhoneNumber ?? "غير محدد",
                        VehicleType = driver?.VehicleType ?? quote.VehicleType ?? "غير محدد",
                        VehicleNumber = driver?.VehicleNumber ?? quote.VehicleNumber ?? "غير محدد",
                        DaysCount = quote.QuotedDays,
                        ProposedAmount = quote.QuotedPrice,
                        IsSelected = quote.Status == QuoteStatus.Accepted,
                        IsWinner = false // يحتاج تحديد منطق الفوز
                    };

                    offers.Add(offer);
                }

                return offers;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الحصول على العروض: {ex.Message}");
                return new List<DriverOffer>();
            }
        }

        /// <summary>
        /// الحصول على عروض الأسعار بالكود الفريد
        /// </summary>
        public async Task<List<DriverOffer>> GetVisitOffersByCodeAsync(string uniqueVisitCode)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔍 البحث عن العروض بالكود الفريد: {uniqueVisitCode}");

                if (string.IsNullOrWhiteSpace(uniqueVisitCode))
                {
                    System.Diagnostics.Debug.WriteLine($"❌ الكود الفريد فارغ");
                    return new List<DriverOffer>();
                }

                var offers = new List<DriverOffer>();

                // البحث في جدول DriverQuotes باستخدام الكود الفريد
                var searchPattern = $"Code: {uniqueVisitCode}";
                var driverQuotes = await _context.DriverQuotes
                    .Where(dq => dq.Notes != null && dq.Notes.Contains(searchPattern))
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"🔍 تم العثور على {driverQuotes.Count} عرض بالكود الفريد");

                // جلب جميع السائقين للمطابقة مع العروض
                var allDrivers = await _context.Drivers.ToListAsync();

                foreach (var quote in driverQuotes)
                {
                    // البحث عن السائق المطابق
                    var driver = allDrivers.FirstOrDefault(d => d.Name == quote.DriverName);

                    if (driver != null)
                    {
                        var offer = new DriverOffer
                        {
                            DriverName = quote.DriverName,
                            DriverCode = driver.DriverCode,
                            PhoneNumber = driver.PhoneNumber,
                            VehicleType = driver.VehicleType,
                            ProposedAmount = quote.QuotedPrice,
                            DaysCount = quote.QuotedDays,
                            VisitNumber = ExtractVisitNumberFromNotes(quote.Notes),
                            IsSelected = quote.Status == QuoteStatus.Accepted,
                            IsWinner = quote.Notes?.Contains("فائز") == true
                        };

                        offers.Add(offer);
                        System.Diagnostics.Debug.WriteLine($"✅ تم تحميل عرض: {offer.DriverName} - {offer.FormattedAmount}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {offers.Count} عرض بالكود الفريد {uniqueVisitCode}");
                return offers.OrderBy(o => o.ProposedAmount).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل العروض بالكود الفريد: {ex.Message}");
                return new List<DriverOffer>();
            }
        }

        /// <summary>
        /// الحصول على عروض الأسعار بالرقم الفريد
        /// </summary>
        public async Task<List<DriverOffer>> GetVisitOffersByUniqueIdAsync(string uniqueVisitId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔍 البحث عن العروض بالرقم الفريد: {uniqueVisitId}");

                if (string.IsNullOrWhiteSpace(uniqueVisitId))
                {
                    System.Diagnostics.Debug.WriteLine($"❌ الرقم الفريد فارغ");
                    return new List<DriverOffer>();
                }

                // تنظيف البيانات المكررة أولاً
                await CleanDuplicateOffersForVisitAsync(uniqueVisitId);

                var offers = new List<DriverOffer>();
                var uniqueDrivers = new HashSet<string>();

                // البحث في جدول DriverQuotes باستخدام VehicleNumber كرقم فريد
                var driverQuotes = await _context.DriverQuotes
                    .Where(dq => dq.VehicleNumber == uniqueVisitId)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"🔍 تم العثور على {driverQuotes.Count} عرض بالرقم الفريد");

                // جلب جميع السائقين للمطابقة مع العروض
                var allDrivers = await _context.Drivers.ToListAsync();

                foreach (var quote in driverQuotes)
                {
                    // تجنب التكرار حتى في القراءة
                    if (!uniqueDrivers.Contains(quote.DriverName))
                    {
                        // البحث عن السائق المطابق
                        var driver = allDrivers.FirstOrDefault(d => d.Name == quote.DriverName);

                        if (driver != null)
                        {
                            var offer = new DriverOffer
                            {
                                DriverName = quote.DriverName,
                                DriverCode = quote.DriverCode,
                                PhoneNumber = quote.PhoneNumber,
                                VehicleType = quote.VehicleType,
                                ProposedAmount = quote.QuotedPrice,
                                DaysCount = quote.QuotedDays,
                                VisitNumber = ExtractVisitNumberFromNotes(quote.Notes),
                                IsSelected = quote.Status == QuoteStatus.Accepted,
                                IsWinner = quote.Notes?.Contains("فائز") == true
                            };

                            offers.Add(offer);
                            uniqueDrivers.Add(quote.DriverName);
                            System.Diagnostics.Debug.WriteLine($"✅ تم تحميل عرض فريد: {offer.DriverName} - {offer.FormattedAmount}");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ تم تجاهل تكرار السائق: {quote.DriverName}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {offers.Count} عرض بالرقم الفريد {uniqueVisitId}");
                return offers.OrderBy(o => o.ProposedAmount).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل العروض بالرقم الفريد: {ex.Message}");
                return new List<DriverOffer>();
            }
        }

        /// <summary>
        /// تنظيف البيانات المكررة لزيارة محددة
        /// </summary>
        private async Task CleanDuplicateOffersForVisitAsync(string uniqueVisitId)
        {
            try
            {
                var allOffers = await _context.DriverQuotes
                    .Where(q => q.VehicleNumber == uniqueVisitId)
                    .OrderBy(q => q.QuoteDate)
                    .ToListAsync();

                var uniqueOffers = new Dictionary<string, DriverQuote>();
                var duplicatesToDelete = new List<DriverQuote>();

                foreach (var offer in allOffers)
                {
                    if (!uniqueOffers.ContainsKey(offer.DriverName))
                    {
                        uniqueOffers[offer.DriverName] = offer;
                    }
                    else
                    {
                        duplicatesToDelete.Add(offer);
                    }
                }

                if (duplicatesToDelete.Any())
                {
                    _context.DriverQuotes.RemoveRange(duplicatesToDelete);
                    await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"🧹 تم حذف {duplicatesToDelete.Count} عرض مكرر للزيارة {uniqueVisitId}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تنظيف البيانات المكررة: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على عروض الأسعار بالرقم
        /// </summary>
        public async Task<List<DriverOffer>> GetVisitOffersByNumberAsync(string visitNumber)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔍 البحث عن العروض للزيارة: {visitNumber}");

                if (string.IsNullOrWhiteSpace(visitNumber))
                {
                    System.Diagnostics.Debug.WriteLine($"❌ رقم الزيارة فارغ");
                    return new List<DriverOffer>();
                }

                var offers = new List<DriverOffer>();

                // البحث في جدول DriverQuotes باستخدام رقم الزيارة (بحث دقيق)
                var searchPattern = $"Visit: {visitNumber}";
                var driverQuotes = await _context.DriverQuotes
                    .Where(dq => dq.Notes != null && dq.Notes.Contains(searchPattern))
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"📊 تم العثور على {driverQuotes.Count} عرض في جدول DriverQuotes للزيارة {visitNumber}");

                System.Diagnostics.Debug.WriteLine($"🔍 تم العثور على {driverQuotes.Count} عرض في قاعدة البيانات");

                // جلب جميع السائقين للمطابقة مع العروض
                var allDrivers = await _context.Drivers.ToListAsync();

                foreach (var quote in driverQuotes)
                {
                    // البحث عن السائق في الجدول الرئيسي لجلب رقم التلفون الصحيح
                    var driver = allDrivers.FirstOrDefault(d =>
                        d.Name == quote.DriverName ||
                        d.DriverCode == quote.DriverCode ||
                        d.Id == quote.DriverId);

                    // تحديد حالة الفوز بناءً على الملاحظات المحفوظة
                    bool isWinner = quote.Notes != null && quote.Notes.Contains("-فائز");
                    bool isSelected = quote.Status == QuoteStatus.Accepted || isWinner;

                    var offer = new DriverOffer
                    {
                        DriverId = quote.DriverId,
                        DriverName = quote.DriverName,
                        DriverCode = quote.DriverCode,
                        PhoneNumber = driver?.PhoneNumber ?? quote.PhoneNumber ?? "غير محدد",
                        VehicleType = driver?.VehicleType ?? quote.VehicleType ?? "غير محدد",
                        VehicleNumber = driver?.VehicleNumber ?? quote.VehicleNumber ?? "غير محدد",
                        DaysCount = quote.QuotedDays,
                        ProposedAmount = quote.QuotedPrice,
                        IsSelected = isSelected,
                        IsWinner = isWinner
                    };

                    offers.Add(offer);
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل عرض: {offer.DriverName} - {offer.FormattedAmount} - فائز: {offer.IsWinner} - محدد: {offer.IsSelected}");
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {offers.Count} عرض للزيارة {visitNumber}");
                return offers;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الحصول على العروض بالرقم: {ex.Message}");
                return new List<DriverOffer>();
            }
        }

        /// <summary>
        /// حذف عروض الأسعار للزيارة بالرقم الفريد
        /// </summary>
        public async Task<bool> DeleteVisitOffersByUniqueIdAsync(string uniqueVisitId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(uniqueVisitId))
                {
                    System.Diagnostics.Debug.WriteLine($"❌ الرقم الفريد فارغ");
                    return false;
                }

                // حذف من جدول DriverQuotes باستخدام VehicleNumber كرقم فريد
                var quotesToDelete = await _context.DriverQuotes
                    .Where(dq => dq.VehicleNumber == uniqueVisitId)
                    .ToListAsync();

                _context.DriverQuotes.RemoveRange(quotesToDelete);
                await _context.SaveChangesAsync();

                System.Diagnostics.Debug.WriteLine($"✅ تم حذف {quotesToDelete.Count} عرض للرقم الفريد {uniqueVisitId}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حذف العروض بالرقم الفريد: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف عروض الأسعار للزيارة بالرقم
        /// </summary>
        public async Task<bool> DeleteVisitOffersAsync(string visitNumber)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(visitNumber))
                {
                    System.Diagnostics.Debug.WriteLine($"❌ رقم الزيارة فارغ");
                    return false;
                }

                // حذف من جدول DriverQuotes باستخدام الرقم الفريد الجديد
                var uniqueVisitId = $"V{visitNumber}";
                var quotesToDelete = await _context.DriverQuotes
                    .Where(dq => dq.VehicleNumber == uniqueVisitId)
                    .ToListAsync();

                if (quotesToDelete.Any())
                {
                    _context.DriverQuotes.RemoveRange(quotesToDelete);
                    await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم حذف {quotesToDelete.Count} عرض للزيارة {visitNumber}");
                }

                // تنظيف البيانات المكررة القديمة أيضاً
                var searchPattern = $"Visit: {visitNumber}";
                var oldQuotesToDelete = await _context.DriverQuotes
                    .Where(dq => dq.Notes != null && dq.Notes.Contains(searchPattern))
                    .ToListAsync();

                if (oldQuotesToDelete.Any())
                {
                    _context.DriverQuotes.RemoveRange(oldQuotesToDelete);
                    await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"🧹 تم حذف {oldQuotesToDelete.Count} عرض قديم للزيارة {visitNumber}");
                }
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حذف العروض: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف عروض الأسعار للزيارة (النسخة القديمة)
        /// </summary>
        public async Task<bool> DeleteVisitOffersAsync(int visitId)
        {
            try
            {
                // حذف من جدول DriverQuotes
                var quotesToDelete = await _context.DriverQuotes
                    .Where(dq => dq.Status == QuoteStatus.Accepted)
                    .ToListAsync();

                _context.DriverQuotes.RemoveRange(quotesToDelete);
                await _context.SaveChangesAsync();

                System.Diagnostics.Debug.WriteLine($"✅ تم حذف {quotesToDelete.Count} عرض");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حذف العروض: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على إحصائيات العروض
        /// </summary>
        public async Task<OffersStatistics> GetOffersStatisticsAsync(int visitId)
        {
            try
            {
                var offers = await GetVisitOffersAsync(visitId);

                if (!offers.Any())
                {
                    return new OffersStatistics();
                }

                var selectedOffers = offers.Where(o => o.IsSelected).ToList();
                var winner = offers.FirstOrDefault(o => o.IsWinner);

                return new OffersStatistics
                {
                    TotalOffers = offers.Count,
                    SelectedOffers = selectedOffers.Count,
                    LowestPrice = offers.Min(o => o.ProposedAmount),
                    HighestPrice = offers.Max(o => o.ProposedAmount),
                    AveragePrice = offers.Average(o => o.ProposedAmount),
                    TotalSelectedAmount = selectedOffers.Sum(o => o.ProposedAmount),
                    WinnerDriverName = winner?.DriverName ?? string.Empty,
                    WinnerAmount = winner?.ProposedAmount ?? 0,
                    LastUpdateDate = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حساب الإحصائيات: {ex.Message}");
                return new OffersStatistics();
            }
        }

        /// <summary>
        /// البحث في العروض
        /// </summary>
        public async Task<List<DriverOffer>> SearchOffersAsync(string searchText)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchText))
                {
                    return new List<DriverOffer>();
                }

                var searchLower = searchText.ToLower();
                var driverQuotes = await _context.DriverQuotes
                    .Where(dq => dq.DriverName.ToLower().Contains(searchLower) ||
                                dq.DriverCode.ToLower().Contains(searchLower) ||
                                dq.PhoneNumber.Contains(searchText))
                    .ToListAsync();

                var offers = new List<DriverOffer>();
                foreach (var quote in driverQuotes)
                {
                    var offer = new DriverOffer
                    {
                        DriverId = quote.DriverId,
                        DriverName = quote.DriverName,
                        DriverCode = quote.DriverCode,
                        PhoneNumber = quote.PhoneNumber,
                        VehicleType = quote.VehicleType,
                        VehicleNumber = quote.VehicleNumber,
                        DaysCount = quote.QuotedDays,
                        ProposedAmount = quote.QuotedPrice,
                        IsSelected = quote.Status == QuoteStatus.Accepted
                    };

                    offers.Add(offer);
                }

                return offers;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في البحث: {ex.Message}");
                return new List<DriverOffer>();
            }
        }

        /// <summary>
        /// الحصول على أفضل عرض للزيارة
        /// </summary>
        public async Task<DriverOffer> GetBestOfferAsync(int visitId)
        {
            try
            {
                var offers = await GetVisitOffersAsync(visitId);
                return offers.Where(o => o.ProposedAmount > 0)
                           .OrderBy(o => o.ProposedAmount)
                           .FirstOrDefault();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الحصول على أفضل عرض: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// تحديث حالة العرض مع حفظ حالة الفوز
        /// </summary>
        public async Task<bool> UpdateOfferStatusAsync(int offerId, bool isSelected, bool isWinner)
        {
            try
            {
                var quote = await _context.DriverQuotes.FindAsync(offerId);
                if (quote != null)
                {
                    quote.Status = isSelected ? QuoteStatus.Accepted : QuoteStatus.Pending;

                    // تحديث الملاحظات لحفظ حالة الفوز
                    var visitInfo = quote.Notes?.Split('-')[0] ?? $"Visit: {quote.Id}";
                    quote.Notes = visitInfo + (isWinner ? "-فائز" : "");

                    await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحديث حالة العرض: {quote.DriverName} - فائز: {isWinner}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث حالة العرض: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحديث حالة الفوز للسائق مع إلغاء فوز الآخرين
        /// </summary>
        public async Task<bool> UpdateWinnerStatusAsync(string visitNumber, string winnerDriverName)
        {
            try
            {
                // جلب جميع العروض للزيارة
                var quotes = await _context.DriverQuotes
                    .Where(q => q.Notes != null && q.Notes.Contains($"Visit: {visitNumber}"))
                    .ToListAsync();

                if (!quotes.Any())
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ لم يتم العثور على عروض للزيارة: {visitNumber}");
                    return false;
                }

                // إلغاء فوز جميع السائقين أولاً
                foreach (var quote in quotes)
                {
                    var visitInfo = quote.Notes?.Split('-')[0] ?? $"Visit: {visitNumber}";
                    quote.Notes = visitInfo; // إزالة "-فائز"
                    quote.Status = QuoteStatus.Pending;
                }

                // تحديد السائق الفائز الجديد
                var winnerQuote = quotes.FirstOrDefault(q => q.DriverName == winnerDriverName);
                if (winnerQuote != null)
                {
                    var visitInfo = winnerQuote.Notes?.Split('-')[0] ?? $"Visit: {visitNumber}";
                    winnerQuote.Notes = visitInfo + "-فائز";
                    winnerQuote.Status = QuoteStatus.Accepted;
                }

                await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"✅ تم تحديث السائق الفائز: {winnerDriverName} للزيارة: {visitNumber}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث السائق الفائز: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إنشاء تقرير العروض
        /// </summary>
        public async Task<OffersReport> GenerateOffersReportAsync(int visitId)
        {
            try
            {
                var offers = await GetVisitOffersAsync(visitId);
                var statistics = await GetOffersStatisticsAsync(visitId);
                var selectedOffers = offers.Where(o => o.IsSelected).ToList();
                var winner = offers.FirstOrDefault(o => o.IsWinner);

                var report = new OffersReport
                {
                    VisitNumber = $"زيارة-{visitId}",
                    ReportDate = DateTime.Now,
                    DaysCount = offers.FirstOrDefault()?.DaysCount ?? 1,
                    AllOffers = offers,
                    SelectedOffers = selectedOffers,
                    WinnerOffer = winner,
                    Statistics = statistics,
                    Summary = GenerateReportSummary(offers, selectedOffers, winner),
                    RecommendationNotes = GenerateRecommendations(offers, statistics)
                };

                return report;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء التقرير: {ex.Message}");
                return new OffersReport();
            }
        }

        #region Private Methods

        /// <summary>
        /// استخراج رقم الزيارة من الملاحظات
        /// </summary>
        private string ExtractVisitNumberFromNotes(string notes)
        {
            try
            {
                if (string.IsNullOrEmpty(notes))
                    return "";

                // البحث عن نمط "Visit: رقم_الزيارة"
                var visitPattern = "Visit: ";
                var startIndex = notes.IndexOf(visitPattern);
                if (startIndex >= 0)
                {
                    startIndex += visitPattern.Length;
                    var endIndex = notes.IndexOf("-", startIndex);
                    if (endIndex > startIndex)
                    {
                        return notes.Substring(startIndex, endIndex - startIndex);
                    }
                    else
                    {
                        // إذا لم يوجد "-" فخذ باقي النص
                        return notes.Substring(startIndex).Trim();
                    }
                }

                return "";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في استخراج رقم الزيارة: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// إنشاء سجل عروض الزيارة
        /// </summary>
        private async Task<bool> CreateVisitOffersRecord(string visitNumber, string offersText, int daysCount)
        {
            try
            {
                // يمكن إنشاء جدول منفصل للعروض لاحقاً
                // مؤقتاً: حفظ في ملف أو متغير
                
                System.Diagnostics.Debug.WriteLine($"📝 حفظ عروض الزيارة {visitNumber}: {offersText}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء سجل العروض: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إنشاء ملخص التقرير
        /// </summary>
        private string GenerateReportSummary(List<DriverOffer> allOffers, List<DriverOffer> selectedOffers, DriverOffer winner)
        {
            var summary = $"تم تقييم {allOffers.Count} عرض سعر، ";
            summary += $"تم اختيار {selectedOffers.Count} عرض. ";
            
            if (winner != null)
            {
                summary += $"الفائز: {winner.DriverName} بمبلغ {winner.FormattedAmount}.";
            }

            return summary;
        }

        /// <summary>
        /// إنشاء توصيات التقرير
        /// </summary>
        private string GenerateRecommendations(List<DriverOffer> offers, OffersStatistics statistics)
        {
            var recommendations = "توصيات:\n";
            
            if (statistics.LowestPrice < statistics.AveragePrice * 0.8m)
            {
                recommendations += "• يُنصح بمراجعة العرض الأقل سعراً للتأكد من جودة الخدمة.\n";
            }
            
            if (statistics.TotalOffers < 3)
            {
                recommendations += "• يُنصح بزيادة عدد العروض للحصول على أفضل الأسعار.\n";
            }

            return recommendations;
        }

        #endregion

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
